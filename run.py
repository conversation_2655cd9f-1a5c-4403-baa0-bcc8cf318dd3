#!/usr/bin/env python3
"""
MCP信号查询演示应用启动脚本
"""

import uvicorn
import os
import sys
from pathlib import Path

def main():
    """启动应用"""
    
    # 确保静态文件目录存在
    static_dir = Path("static")
    if not static_dir.exists():
        print("错误: static目录不存在")
        sys.exit(1)
    
    # 检查HTML文件
    index_file = static_dir / "index.html"
    if not index_file.exists():
        print("错误: static/index.html文件不存在")
        sys.exit(1)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("警告: DEEPSEEK_API_KEY环境变量未设置")
    
    if not os.getenv("MCP_SERVER_URL"):
        print("警告: MCP_SERVER_URL环境变量未设置")
    
    print("启动MCP信号查询演示应用...")
    print("访问地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务")
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
