from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import H<PERSON><PERSON>esponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import httpx
import json
import os
from dotenv import load_dotenv
from typing import Dict, Any, List, Optional
import asyncio
import logging
from mcp_client import MCPClient, MCPToolRegistry

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="MCP Signal Query Demo", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL")

# 初始化MCP工具注册表
mcp_registry = MCPToolRegistry()

class QueryRequest(BaseModel):
    query: str
    project_key: Optional[str] = None



class DeepSeekClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1"
    
    async def chat_completion(self, messages: List[Dict[str, str]], tools: List[Dict] = None) -> Dict[str, Any]:
        """调用DeepSeek聊天完成API"""
        async with httpx.AsyncClient(timeout=60.0) as client:
            payload = {
                "model": "deepseek-chat",
                "messages": messages,
                "temperature": 0.1,
                "max_tokens": 2000
            }
            
            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            try:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"DeepSeek API调用失败: {response.status_code} - {response.text}")
                    return {"error": f"DeepSeek API调用失败: {response.status_code}"}
                    
            except Exception as e:
                logger.error(f"DeepSeek API调用异常: {str(e)}")
                return {"error": f"DeepSeek API调用异常: {str(e)}"}



@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页HTML"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>页面未找到</h1><p>请确保static/index.html文件存在</p>", status_code=404)

@app.post("/api/query")
async def query_signal(request: QueryRequest):
    """处理信号查询请求"""
    try:
        # 准备系统消息
        system_message = {
            "role": "system",
            "content": """你是一个专业的CAN信号查询助手。用户会询问关于CAN信号的问题，你需要：
1. 理解用户的查询意图
2. 选择合适的MCP工具来查询信号信息
3. 以友好、专业的方式回答用户的问题

可用的工具：
- get_available_projects: 获取可用项目列表
- search_signals_fuzzy: 模糊搜索信号（支持中文关键词）
- get_signal_exact: 精确查询特定信号

请根据用户的查询选择最合适的工具。"""
        }
        
        user_message = {
            "role": "user", 
            "content": request.query
        }
        
        messages = [system_message, user_message]
        
        # 调用DeepSeek API
        deepseek_client = DeepSeekClient(DEEPSEEK_API_KEY)
        response = await deepseek_client.chat_completion(messages, mcp_registry.get_tools_for_ai())
        
        if "error" in response:
            raise HTTPException(status_code=500, detail=response["error"])
        
        # 处理工具调用
        choice = response["choices"][0]
        message = choice["message"]
        
        tool_calls = []
        mcp_results = []
        
        if message.get("tool_calls"):
            async with MCPClient(MCP_SERVER_URL) as mcp_client:
                for tool_call in message["tool_calls"]:
                    function_name = tool_call["function"]["name"]
                    arguments = json.loads(tool_call["function"]["arguments"])
                    
                    # 如果用户指定了项目，添加到参数中
                    if request.project_key and "project_key" not in arguments:
                        arguments["project_key"] = request.project_key
                    
                    # 调用MCP工具
                    mcp_result = await mcp_client.call_tool(function_name, arguments)
                    
                    tool_calls.append({
                        "function": function_name,
                        "arguments": arguments,
                        "result": mcp_result
                    })
                    
                    mcp_results.append({
                        "role": "tool",
                        "tool_call_id": tool_call["id"],
                        "content": json.dumps(mcp_result, ensure_ascii=False)
                    })
        
        # 如果有工具调用结果，再次调用AI生成最终回答
        final_response = message.get("content", "")
        
        if mcp_results:
            messages.append(message)
            messages.extend(mcp_results)
            
            final_ai_response = await deepseek_client.chat_completion(messages)
            if "choices" in final_ai_response:
                final_response = final_ai_response["choices"][0]["message"]["content"]
        
        return {
            "success": True,
            "query": request.query,
            "response": final_response,
            "tool_calls": tool_calls,
            "raw_ai_response": response
        }
        
    except Exception as e:
        logger.error(f"查询处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询处理失败: {str(e)}")

@app.get("/api/projects")
async def get_projects():
    """获取可用项目列表"""
    try:
        async with MCPClient(MCP_SERVER_URL) as mcp_client:
            result = await mcp_client.call_tool("get_available_projects", {})
            return result
    except Exception as e:
        logger.error(f"获取项目列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """系统健康检查"""
    health_status = {
        "status": "healthy",
        "deepseek_api": "configured" if DEEPSEEK_API_KEY else "not_configured",
        "mcp_server": "unknown",
        "timestamp": "2025-01-25T12:00:00Z"
    }

    # 检查MCP服务器连接
    try:
        async with MCPClient(MCP_SERVER_URL) as mcp_client:
            is_healthy = await mcp_client.health_check()
            health_status["mcp_server"] = "healthy" if is_healthy else "unhealthy"
    except Exception as e:
        health_status["mcp_server"] = "error"
        health_status["mcp_error"] = str(e)

    return health_status

@app.get("/api/tools")
async def get_available_tools():
    """获取可用的MCP工具列表"""
    return {
        "tools": mcp_registry.get_tool_names(),
        "count": len(mcp_registry.get_tool_names()),
        "definitions": mcp_registry.get_tools_for_ai()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
