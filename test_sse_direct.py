#!/usr/bin/env python3
"""
直接测试SSE连接
"""

import asyncio
import httpx
from httpx_sse import aconnect_sse
import json

async def test_sse_connection():
    """测试SSE连接"""
    url = "http://113.45.150.139:33398/sse/"
    
    print(f"🔗 直接测试SSE连接: {url}")
    print("=" * 60)
    
    async with httpx.AsyncClient(follow_redirects=True) as client:
        try:
            # 1. 先测试GET请求
            print("1. 测试GET请求...")
            response = await client.get(url)
            print(f"   状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"   响应长度: {len(response.content)}")
            if response.content:
                print(f"   响应内容前100字符: {response.content[:100]}")
            print()
            
            # 2. 测试SSE连接
            print("2. 测试SSE连接...")
            try:
                async with aconnect_sse(
                    client,
                    "GET",
                    url,
                    headers={"Accept": "text/event-stream"}
                ) as event_source:
                    print("   ✅ SSE连接成功")
                    
                    # 读取前几个事件
                    event_count = 0
                    async for sse_event in event_source.aiter_sse():
                        event_count += 1
                        print(f"   事件 {event_count}:")
                        print(f"     类型: {sse_event.event}")
                        print(f"     数据: {sse_event.data[:200]}...")
                        
                        if event_count >= 3:  # 只读取前3个事件
                            break
                            
            except Exception as e:
                print(f"   ❌ SSE连接失败: {str(e)}")
            print()
            
            # 3. 测试发送MCP请求
            print("3. 测试发送MCP请求...")
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list"
            }
            
            try:
                async with aconnect_sse(
                    client,
                    "POST",
                    url,
                    json=mcp_request,
                    headers={"Accept": "text/event-stream", "Content-Type": "application/json"}
                ) as event_source:
                    print("   ✅ MCP请求发送成功")
                    
                    # 等待响应
                    async for sse_event in event_source.aiter_sse():
                        print(f"   响应事件:")
                        print(f"     类型: {sse_event.event}")
                        print(f"     数据: {sse_event.data}")
                        
                        try:
                            data = json.loads(sse_event.data)
                            if "id" in data and data["id"] == 1:
                                print(f"   ✅ 收到匹配的响应: {data}")
                                break
                        except json.JSONDecodeError:
                            continue
                            
            except Exception as e:
                print(f"   ❌ MCP请求失败: {str(e)}")
            print()
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")

async def test_different_methods():
    """测试不同的请求方法"""
    url = "http://113.45.150.139:33398/sse/"
    
    print("🔍 测试不同的请求方法...")
    print("=" * 60)
    
    async with httpx.AsyncClient(follow_redirects=True) as client:
        methods = ["GET", "POST", "OPTIONS"]
        
        for method in methods:
            print(f"测试 {method} 请求...")
            try:
                if method == "GET":
                    response = await client.get(url, headers={"Accept": "text/event-stream"})
                elif method == "POST":
                    response = await client.post(url, 
                                               json={"jsonrpc": "2.0", "id": 1, "method": "ping"},
                                               headers={"Accept": "text/event-stream"})
                elif method == "OPTIONS":
                    response = await client.options(url)
                
                print(f"  状态码: {response.status_code}")
                print(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
                print(f"  响应长度: {len(response.content)}")
                
                if response.status_code == 200 and "event-stream" in response.headers.get('content-type', ''):
                    print(f"  ✅ 成功获得SSE流")
                else:
                    print(f"  ❌ 非SSE响应")
                    
            except Exception as e:
                print(f"  ❌ 请求失败: {str(e)}")
            print()

async def main():
    """主函数"""
    await test_sse_connection()
    print("\n" + "=" * 60)
    await test_different_methods()

if __name__ == "__main__":
    asyncio.run(main())
