import httpx
import json
import logging
from typing import Dict, Any, Optional
import asyncio

logger = logging.getLogger(__name__)

class MCPClient:
    """MCP客户端，用于与远程MCP服务器通信"""
    
    def __init__(self, server_url: str):
        self.server_url = server_url.rstrip('/')
        self.session: Optional[httpx.AsyncClient] = None
        self.timeout = 30.0
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={"Content-Type": "application/json"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.aclose()
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        if not self.session:
            raise RuntimeError("MCP客户端未初始化，请使用async with语句")
        
        try:
            payload = {
                "tool": tool_name,
                "arguments": arguments
            }
            
            logger.info(f"调用MCP工具: {tool_name}, 参数: {arguments}")
            
            response = await self.session.post(
                f"{self.server_url}/call_tool",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"MCP工具调用成功: {tool_name}")
                return result
            else:
                error_msg = f"MCP调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.text
                    if error_detail:
                        error_msg += f" - {error_detail}"
                except:
                    pass
                
                logger.error(error_msg)
                return {"error": error_msg}
                
        except httpx.TimeoutException:
            error_msg = f"MCP调用超时: {tool_name}"
            logger.error(error_msg)
            return {"error": error_msg}
        except httpx.ConnectError:
            error_msg = f"无法连接到MCP服务器: {self.server_url}"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"MCP调用异常: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    async def get_available_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        try:
            if not self.session:
                raise RuntimeError("MCP客户端未初始化")
            
            response = await self.session.get(f"{self.server_url}/tools")
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"获取工具列表失败: HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"获取工具列表异常: {str(e)}")
            return {"error": f"获取工具列表异常: {str(e)}"}
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.session:
                return False
            
            response = await self.session.get(
                f"{self.server_url}/health",
                timeout=5.0
            )
            return response.status_code == 200
            
        except Exception:
            return False

class MCPToolRegistry:
    """MCP工具注册表，管理可用的工具定义"""
    
    def __init__(self):
        self.tools = {}
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认的MCP工具"""
        
        # 获取可用项目
        self.tools["get_available_projects"] = {
            "type": "function",
            "function": {
                "name": "get_available_projects",
                "description": "获取可用项目列表",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
        
        # 切换项目
        self.tools["switch_project"] = {
            "type": "function",
            "function": {
                "name": "switch_project",
                "description": "切换当前活动项目",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)"
                        }
                    },
                    "required": ["project_key"]
                }
            }
        }
        
        # 模糊搜索信号
        self.tools["search_signals_fuzzy"] = {
            "type": "function", 
            "function": {
                "name": "search_signals_fuzzy",
                "description": "模糊查询CAN信号信息（支持中文关键词搜索）",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "keyword": {
                            "type": "string",
                            "description": "搜索关键词（支持部分匹配，可搜索信号名称、注释、值描述）"
                        },
                        "case_sensitive": {
                            "type": "boolean",
                            "description": "是否区分大小写，默认False"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "最大返回结果数量，默认50"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["keyword"]
                }
            }
        }
        
        # 精确查询信号
        self.tools["get_signal_exact"] = {
            "type": "function",
            "function": {
                "name": "get_signal_exact",
                "description": "精确查询CAN信号信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "signal_name": {
                            "type": "string",
                            "description": "信号名称（精确匹配）"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["signal_name"]
                }
            }
        }
        
        # 查询消息信息
        self.tools["get_message_info"] = {
            "type": "function",
            "function": {
                "name": "get_message_info",
                "description": "查询CAN消息信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message_name": {
                            "type": "string",
                            "description": "消息名称"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["message_name"]
                }
            }
        }
        
        # 获取项目统计信息
        self.tools["get_project_statistics"] = {
            "type": "function",
            "function": {
                "name": "get_project_statistics",
                "description": "获取项目统计信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则返回所有项目统计信息"
                        }
                    },
                    "required": []
                }
            }
        }
    
    def get_tools_for_ai(self) -> list:
        """获取用于AI的工具定义列表"""
        return list(self.tools.values())
    
    def get_tool_names(self) -> list:
        """获取所有工具名称"""
        return list(self.tools.keys())
    
    def is_valid_tool(self, tool_name: str) -> bool:
        """检查工具是否有效"""
        return tool_name in self.tools
