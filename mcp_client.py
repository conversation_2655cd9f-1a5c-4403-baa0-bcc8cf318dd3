import logging
import json
from typing import Dict, Any, Optional, List
import httpx
from httpx_sse import aconnect_sse
import asyncio

logger = logging.getLogger(__name__)

class MCPClient:
    """MCP客户端，使用SSE与远程MCP服务器通信"""

    def __init__(self, server_url: str):
        self.server_url = server_url.rstrip('/')
        self.http_client: Optional[httpx.AsyncClient] = None
        self.timeout = 30.0
        self._request_id = 0
        self._sse_connection = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        try:
            # 创建HTTP客户端
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers={"Content-Type": "application/json"}
            )

            logger.info(f"MCP客户端初始化成功: {self.server_url}")
            return self

        except Exception as e:
            logger.error(f"MCP客户端初始化失败: {str(e)}")
            return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.http_client:
            try:
                await self.http_client.aclose()
            except Exception as e:
                logger.error(f"关闭HTTP客户端时出错: {str(e)}")

    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self._request_id += 1
        return self._request_id

    async def _connect_sse(self) -> bool:
        """建立SSE连接"""
        if not self.http_client:
            return False

        try:
            # 建立SSE连接
            self._sse_connection = aconnect_sse(
                self.http_client,
                "GET",
                self.server_url,
                headers={"Accept": "text/event-stream"}
            )
            return True
        except Exception as e:
            logger.error(f"建立SSE连接失败: {str(e)}")
            return False

    async def _send_mcp_request_via_sse(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """通过SSE发送MCP请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }

        if params:
            request_data["params"] = params

        try:
            # 由于这个SSE服务器只支持GET，我们需要通过查询参数发送请求
            # 或者使用WebSocket等其他方式
            # 暂时回退到HTTP调用
            return await self._fallback_http_call(method, params)

        except Exception as e:
            logger.error(f"SSE请求失败: {str(e)}")
            return {"error": f"SSE请求失败: {str(e)}"}

    async def _send_mcp_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送MCP请求"""
        # 由于远程服务器的SSE端点特性，直接使用HTTP回退
        return await self._fallback_http_call(method, params)

    async def _try_different_endpoints(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """尝试不同的端点格式"""
        base_url = self.server_url.replace('/sse/', '').replace('/sse', '')

        # 可能的端点格式
        possible_endpoints = [
            f"{base_url}/api",
            f"{base_url}/mcp",
            f"{base_url}/rpc",
            f"{base_url}",
            f"{base_url}/jsonrpc",
        ]

        if method == "tools/call" and params:
            tool_name = params.get("name")
            arguments = params.get("arguments", {})

            # 尝试不同的工具调用格式
            for endpoint in possible_endpoints:
                try:
                    # 格式1: 标准MCP格式
                    mcp_payload = {
                        "jsonrpc": "2.0",
                        "id": self._get_next_request_id(),
                        "method": "tools/call",
                        "params": {
                            "name": tool_name,
                            "arguments": arguments
                        }
                    }

                    response = await self.http_client.post(endpoint, json=mcp_payload)
                    if response.status_code == 200:
                        return response.json()

                    # 格式2: 简化格式
                    simple_payload = {
                        "tool": tool_name,
                        "arguments": arguments
                    }

                    response = await self.http_client.post(f"{endpoint}/call_tool", json=simple_payload)
                    if response.status_code == 200:
                        return response.json()

                except Exception:
                    continue

        elif method == "tools/list":
            for endpoint in possible_endpoints:
                try:
                    # 格式1: MCP格式
                    mcp_payload = {
                        "jsonrpc": "2.0",
                        "id": self._get_next_request_id(),
                        "method": "tools/list"
                    }

                    response = await self.http_client.post(endpoint, json=mcp_payload)
                    if response.status_code == 200:
                        return response.json()

                    # 格式2: GET请求
                    response = await self.http_client.get(f"{endpoint}/tools")
                    if response.status_code == 200:
                        return response.json()

                except Exception:
                    continue

        return {"error": f"所有端点都无法处理方法: {method}"}

    async def _fallback_http_call(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """HTTP回退调用方法"""
        try:
            # 首先尝试标准端点
            if method == "tools/call" and params:
                tool_name = params.get("name")
                arguments = params.get("arguments", {})

                payload = {
                    "tool": tool_name,
                    "arguments": arguments
                }

                response = await self.http_client.post(
                    f"{self.server_url}/call_tool",
                    json=payload
                )

                if response.status_code == 200:
                    return response.json()

            elif method == "tools/list":
                response = await self.http_client.get(f"{self.server_url}/tools")
                if response.status_code == 200:
                    return response.json()

            # 如果标准端点失败，尝试其他端点
            return await self._try_different_endpoints(method, params)

        except Exception as e:
            return {"error": f"HTTP调用异常: {str(e)}"}

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        logger.info(f"调用MCP工具: {tool_name}, 参数: {arguments}")

        try:
            # 使用MCP协议调用工具
            result = await self._send_mcp_request(
                "tools/call",
                {
                    "name": tool_name,
                    "arguments": arguments
                }
            )

            if "error" in result:
                logger.error(f"MCP工具调用错误: {result['error']}")
                return result

            logger.info(f"MCP工具调用成功: {tool_name}")

            # 处理MCP响应格式
            if "result" in result:
                return result["result"]
            else:
                return result

        except Exception as e:
            logger.error(f"MCP工具调用异常: {str(e)}")
            return {"error": f"MCP工具调用异常: {str(e)}"}

    async def get_available_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        try:
            result = await self._send_mcp_request("tools/list")

            if "error" in result:
                logger.error(f"获取工具列表失败: {result['error']}")
                return result

            # 处理MCP响应格式
            if "result" in result and "tools" in result["result"]:
                return result["result"]
            else:
                return result

        except Exception as e:
            logger.error(f"获取工具列表异常: {str(e)}")
            return {"error": f"获取工具列表异常: {str(e)}"}

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.http_client:
                return False

            # 尝试简单的HTTP请求
            response = await self.http_client.get(
                f"{self.server_url}",
                timeout=5.0
            )
            return response.status_code in [200, 404]  # 404也可能表示服务器在运行

        except Exception as e:
            logger.debug(f"健康检查失败: {str(e)}")
            return False

class MCPToolRegistry:
    """MCP工具注册表，管理可用的工具定义"""
    
    def __init__(self):
        self.tools = {}
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认的MCP工具"""
        
        # 获取可用项目
        self.tools["get_available_projects"] = {
            "type": "function",
            "function": {
                "name": "get_available_projects",
                "description": "获取可用项目列表",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
        
        # 切换项目
        self.tools["switch_project"] = {
            "type": "function",
            "function": {
                "name": "switch_project",
                "description": "切换当前活动项目",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)"
                        }
                    },
                    "required": ["project_key"]
                }
            }
        }
        
        # 模糊搜索信号
        self.tools["search_signals_fuzzy"] = {
            "type": "function", 
            "function": {
                "name": "search_signals_fuzzy",
                "description": "模糊查询CAN信号信息（支持中文关键词搜索）",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "keyword": {
                            "type": "string",
                            "description": "搜索关键词（支持部分匹配，可搜索信号名称、注释、值描述）"
                        },
                        "case_sensitive": {
                            "type": "boolean",
                            "description": "是否区分大小写，默认False"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "最大返回结果数量，默认50"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["keyword"]
                }
            }
        }
        
        # 精确查询信号
        self.tools["get_signal_exact"] = {
            "type": "function",
            "function": {
                "name": "get_signal_exact",
                "description": "精确查询CAN信号信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "signal_name": {
                            "type": "string",
                            "description": "信号名称（精确匹配）"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["signal_name"]
                }
            }
        }
        
        # 查询消息信息
        self.tools["get_message_info"] = {
            "type": "function",
            "function": {
                "name": "get_message_info",
                "description": "查询CAN消息信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message_name": {
                            "type": "string",
                            "description": "消息名称"
                        },
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                        }
                    },
                    "required": ["message_name"]
                }
            }
        }
        
        # 获取项目统计信息
        self.tools["get_project_statistics"] = {
            "type": "function",
            "function": {
                "name": "get_project_statistics",
                "description": "获取项目统计信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project_key": {
                            "type": "string",
                            "description": "项目键值 (F2, F3)，如果不指定则返回所有项目统计信息"
                        }
                    },
                    "required": []
                }
            }
        }
    
    def get_tools_for_ai(self) -> list:
        """获取用于AI的工具定义列表"""
        return list(self.tools.values())
    
    def get_tool_names(self) -> list:
        """获取所有工具名称"""
        return list(self.tools.keys())
    
    def is_valid_tool(self, tool_name: str) -> bool:
        """检查工具是否有效"""
        return tool_name in self.tools
