#!/usr/bin/env python3
"""
MCP连接测试脚本
"""

import asyncio
import logging
from mcp_client import MCPClient
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_mcp_connection():
    """测试MCP连接"""
    mcp_url = os.getenv("MCP_SERVER_URL", "http://113.45.150.139:33398/sse/")
    
    print(f"🔗 测试MCP服务器连接: {mcp_url}")
    print("=" * 60)
    
    async with MCPClient(mcp_url) as client:
        # 1. 健康检查
        print("1. 健康检查...")
        is_healthy = await client.health_check()
        print(f"   结果: {'✅ 健康' if is_healthy else '❌ 不健康'}")
        print()
        
        # 2. 获取工具列表
        print("2. 获取可用工具列表...")
        tools_result = await client.get_available_tools()
        if "error" in tools_result:
            print(f"   ❌ 错误: {tools_result['error']}")
        else:
            print(f"   ✅ 成功获取工具列表")
            if "tools" in tools_result:
                print(f"   📋 可用工具数量: {len(tools_result['tools'])}")
                for tool in tools_result['tools'][:3]:  # 只显示前3个
                    print(f"      - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
            else:
                print(f"   📋 响应内容: {tools_result}")
        print()
        
        # 3. 测试工具调用
        print("3. 测试工具调用...")
        
        # 测试获取可用项目
        print("   3.1 调用 get_available_projects...")
        result1 = await client.call_tool("get_available_projects", {})
        if "error" in result1:
            print(f"       ❌ 错误: {result1['error']}")
        else:
            print(f"       ✅ 成功")
            print(f"       📊 结果: {result1}")
        print()
        
        # 测试模糊搜索
        print("   3.2 调用 search_signals_fuzzy...")
        result2 = await client.call_tool("search_signals_fuzzy", {
            "keyword": "speed",
            "max_results": 5
        })
        if "error" in result2:
            print(f"       ❌ 错误: {result2['error']}")
        else:
            print(f"       ✅ 成功")
            print(f"       📊 结果: {result2}")
        print()
        
        # 测试项目统计
        print("   3.3 调用 get_project_statistics...")
        result3 = await client.call_tool("get_project_statistics", {})
        if "error" in result3:
            print(f"       ❌ 错误: {result3['error']}")
        else:
            print(f"       ✅ 成功")
            print(f"       📊 结果: {result3}")
        print()

async def test_different_endpoints():
    """测试不同的端点格式"""
    base_url = "http://113.45.150.139:33398"
    endpoints = [
        f"{base_url}/sse/",
        f"{base_url}/sse",
        f"{base_url}/",
        f"{base_url}",
    ]
    
    print("🔍 测试不同端点格式...")
    print("=" * 60)
    
    for endpoint in endpoints:
        print(f"测试端点: {endpoint}")
        try:
            async with MCPClient(endpoint) as client:
                is_healthy = await client.health_check()
                print(f"  健康检查: {'✅' if is_healthy else '❌'}")
                
                if is_healthy:
                    tools_result = await client.get_available_tools()
                    if "error" not in tools_result:
                        print(f"  工具列表: ✅")
                        break
                    else:
                        print(f"  工具列表: ❌ {tools_result['error']}")
                else:
                    print(f"  工具列表: ⏭️ 跳过")
        except Exception as e:
            print(f"  异常: ❌ {str(e)}")
        print()

async def main():
    """主函数"""
    print("🚀 MCP连接测试开始")
    print("=" * 60)
    
    # 测试主要连接
    await test_mcp_connection()
    
    print("\n" + "=" * 60)
    
    # 测试不同端点
    await test_different_endpoints()
    
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
