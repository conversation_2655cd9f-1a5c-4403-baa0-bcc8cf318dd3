#!/usr/bin/env python3
"""
测试不同的端点
"""

import asyncio
import httpx
import json

async def test_endpoint(url, method="GET", data=None):
    """测试单个端点"""
    print(f"测试: {method} {url}")
    
    async with httpx.AsyncClient(follow_redirects=True, timeout=10.0) as client:
        try:
            if method == "GET":
                response = await client.get(url)
            elif method == "POST":
                if data:
                    response = await client.post(url, json=data)
                else:
                    response = await client.post(url)
            
            print(f"  状态码: {response.status_code}")
            print(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"  响应长度: {len(response.content)}")
            
            if response.content:
                content_preview = response.content[:200].decode('utf-8', errors='ignore')
                print(f"  响应内容: {content_preview}")
            
            return response.status_code, response.headers, response.content
            
        except Exception as e:
            print(f"  ❌ 错误: {str(e)}")
            return None, None, None
        
        print()

async def main():
    """主函数"""
    base_url = "http://113.45.150.139:33398"
    
    # 测试不同的端点
    endpoints = [
        f"{base_url}",
        f"{base_url}/",
        f"{base_url}/sse",
        f"{base_url}/sse/",
        f"{base_url}/api",
        f"{base_url}/api/",
        f"{base_url}/mcp",
        f"{base_url}/mcp/",
    ]
    
    print("🔍 测试GET请求...")
    print("=" * 60)
    
    for endpoint in endpoints:
        await test_endpoint(endpoint, "GET")
        print()
    
    print("🔍 测试POST请求...")
    print("=" * 60)
    
    # 测试MCP请求
    mcp_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/list"
    }
    
    for endpoint in endpoints:
        await test_endpoint(endpoint, "POST", mcp_request)
        print()
    
    # 测试工具调用
    print("🔍 测试工具调用...")
    print("=" * 60)
    
    tool_request = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": "get_available_projects",
            "arguments": {}
        }
    }
    
    for endpoint in endpoints:
        await test_endpoint(endpoint, "POST", tool_request)
        print()

if __name__ == "__main__":
    asyncio.run(main())
