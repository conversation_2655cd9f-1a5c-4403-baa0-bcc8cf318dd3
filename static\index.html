<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP信号查询演示</title>
    
    <!-- CSS Dependencies -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .chat-container {
            height: calc(100vh - 200px);
        }
        .message-bubble {
            animation: fadeInUp 0.3s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .loading-dots {
            display: inline-block;
        }
        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        .tool-call-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .mcp-capability-card {
            transition: all 0.3s ease;
        }
        .mcp-capability-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-microchip text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">MCP信号查询演示</h1>
                        <p class="text-sm text-gray-500">基于DeepSeek AI + MCP协议的智能CAN信号查询系统</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>MCP服务已连接</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 主对话区域 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border">
                    <!-- 对话标题 -->
                    <div class="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-comments text-blue-500 mr-2"></i>
                            智能信号查询对话
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">输入您的查询需求，AI将调用MCP工具为您查找相关信号信息</p>
                    </div>
                    
                    <!-- 聊天消息区域 -->
                    <div id="chatMessages" class="chat-container overflow-y-auto p-6 space-y-4">
                        <!-- 欢迎消息 -->
                        <div class="message-bubble flex items-start space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-md">
                                <p class="text-gray-800">您好！我是MCP信号查询助手。您可以：</p>
                                <ul class="mt-2 text-sm text-gray-600 space-y-1">
                                    <li>• 搜索特定信号：如"查找转速信号"</li>
                                    <li>• 精确查询：如"获取EngineSpeed信号详情"</li>
                                    <li>• 查看项目信息：如"显示可用项目"</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="px-6 py-4 border-t bg-gray-50">
                        <div class="flex space-x-3">
                            <div class="flex-1">
                                <div class="relative">
                                    <input 
                                        type="text" 
                                        id="queryInput" 
                                        placeholder="请输入您的查询需求，例如：查找转速相关的信号..."
                                        class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        onkeypress="handleKeyPress(event)"
                                    >
                                    <button 
                                        onclick="sendQuery()" 
                                        id="sendButton"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center"
                                    >
                                        <i class="fas fa-paper-plane text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                            <span>按 Enter 发送消息</span>
                            <span id="projectInfo">当前项目：自动选择</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- MCP能力展示区域 -->
            <div class="space-y-6">
                <!-- MCP工具能力卡片 -->
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="px-6 py-4 border-b">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-tools text-purple-500 mr-2"></i>
                            MCP工具能力
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="mcp-capability-card bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-search text-blue-600"></i>
                                <div>
                                    <h4 class="font-medium text-blue-900">模糊信号搜索</h4>
                                    <p class="text-sm text-blue-700">支持中文关键词的智能信号搜索</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mcp-capability-card bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-crosshairs text-green-600"></i>
                                <div>
                                    <h4 class="font-medium text-green-900">精确信号查询</h4>
                                    <p class="text-sm text-green-700">根据信号名称精确获取详细信息</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mcp-capability-card bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-project-diagram text-purple-600"></i>
                                <div>
                                    <h4 class="font-medium text-purple-900">项目管理</h4>
                                    <p class="text-sm text-purple-700">查看和切换不同的CAN数据库项目</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统状态 -->
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="px-6 py-4 border-b">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-server text-green-500 mr-2"></i>
                            系统状态
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">DeepSeek AI</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-green-600">已连接</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">MCP服务器</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-green-600">已连接</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">可用项目</span>
                            <span class="text-sm text-blue-600" id="projectCount">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        // 页面加载时获取项目信息
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
        });

        async function loadProjects() {
            try {
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                if (data.total_projects !== undefined) {
                    document.getElementById('projectCount').textContent = `${data.total_projects}个`;
                    if (data.current_project) {
                        document.getElementById('projectInfo').textContent = `当前项目：${data.current_project}`;
                    }
                }
            } catch (error) {
                console.error('加载项目信息失败:', error);
                document.getElementById('projectCount').textContent = '加载失败';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isLoading) {
                sendQuery();
            }
        }

        async function sendQuery() {
            const input = document.getElementById('queryInput');
            const query = input.value.trim();
            
            if (!query || isLoading) return;
            
            isLoading = true;
            const sendButton = document.getElementById('sendButton');
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
            
            // 添加用户消息
            addMessage(query, 'user');
            input.value = '';
            
            // 添加加载消息
            const loadingId = addMessage('正在查询中<span class="loading-dots"></span>', 'assistant', true);
            
            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                });
                
                const data = await response.json();
                
                // 移除加载消息
                document.getElementById(loadingId).remove();
                
                if (data.success) {
                    // 添加AI回复
                    addMessage(data.response, 'assistant');
                    
                    // 如果有工具调用，显示工具调用详情
                    if (data.tool_calls && data.tool_calls.length > 0) {
                        addToolCallsDisplay(data.tool_calls);
                    }
                } else {
                    addMessage('抱歉，查询过程中出现了错误。请稍后重试。', 'assistant');
                }
                
            } catch (error) {
                console.error('查询失败:', error);
                document.getElementById(loadingId).remove();
                addMessage('网络连接错误，请检查网络后重试。', 'assistant');
            }
            
            isLoading = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane text-sm"></i>';
        }

        function addMessage(content, sender, isLoading = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            const messageDiv = document.createElement('div');
            messageDiv.id = messageId;
            messageDiv.className = 'message-bubble flex items-start space-x-3';
            
            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="bg-blue-500 text-white rounded-lg px-4 py-3 max-w-md ml-auto">
                        <p>${content}</p>
                    </div>
                `;
                messageDiv.classList.add('flex-row-reverse');
            } else {
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-md">
                        <p class="text-gray-800">${content}</p>
                    </div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageId;
        }

        function addToolCallsDisplay(toolCalls) {
            const messagesContainer = document.getElementById('chatMessages');
            
            toolCalls.forEach(toolCall => {
                const toolDiv = document.createElement('div');
                toolDiv.className = 'message-bubble';
                toolDiv.innerHTML = `
                    <div class="tool-call-card rounded-lg p-4 text-white mb-4">
                        <div class="flex items-center space-x-2 mb-3">
                            <i class="fas fa-cog"></i>
                            <span class="font-medium">MCP工具调用: ${toolCall.function}</span>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded p-3 text-sm">
                            <div class="mb-2">
                                <strong>参数:</strong>
                                <pre class="mt-1 text-xs">${JSON.stringify(toolCall.arguments, null, 2)}</pre>
                            </div>
                            <div>
                                <strong>结果:</strong>
                                <pre class="mt-1 text-xs max-h-32 overflow-y-auto">${JSON.stringify(toolCall.result, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                `;
                
                messagesContainer.appendChild(toolDiv);
            });
            
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    </script>
</body>
</html>
