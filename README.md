# MCP信号查询演示应用

这是一个基于FastAPI + DeepSeek AI + MCP协议的智能CAN信号查询演示应用。

## 功能特性

- 🤖 **智能对话界面**: 基于DeepSeek AI的自然语言查询
- 🔧 **MCP工具集成**: 通过MCP协议调用远程CAN信号查询工具
- 🎨 **精美UI设计**: 使用Tailwind CSS和Font Awesome的现代化界面
- 📊 **实时交互展示**: 可视化AI与MCP工具的交互过程
- 🚀 **高性能异步**: FastAPI异步架构，支持高并发

## 系统架构

```
用户输入 → 前端界面 → FastAPI后端 → DeepSeek AI → MCP工具调用 → 返回结果
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件或设置环境变量：

```bash
DEEPSEEK_API_KEY=***********************************
MCP_SERVER_URL=http://**************:33398/sse/
```

### 3. 启动应用

```bash
python run.py
```

或者直接使用uvicorn：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问应用

- 主页面: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/health

## 使用说明

### 支持的查询类型

1. **模糊搜索信号**
   - 示例: "查找转速相关的信号"
   - 示例: "搜索发动机温度信号"

2. **精确查询信号**
   - 示例: "获取EngineSpeed信号详情"
   - 示例: "查询具体信号名称"

3. **项目管理**
   - 示例: "显示可用项目"
   - 示例: "切换到F2项目"

### MCP工具能力

- `get_available_projects`: 获取可用项目列表
- `switch_project`: 切换当前活动项目
- `search_signals_fuzzy`: 模糊搜索CAN信号
- `get_signal_exact`: 精确查询CAN信号
- `get_message_info`: 查询CAN消息信息
- `get_project_statistics`: 获取项目统计信息

## 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **DeepSeek API**: 大语言模型服务
- **httpx**: 异步HTTP客户端
- **pydantic**: 数据验证和序列化

### 前端
- **HTML5**: 现代化的标记语言
- **Tailwind CSS**: 实用优先的CSS框架
- **Font Awesome**: 图标库
- **JavaScript**: 原生JavaScript实现交互

### MCP协议
- **远程MCP服务器**: 通过SSE连接
- **工具调用**: 支持多种CAN信号查询工具

## API接口

### POST /api/query
查询信号信息

**请求体:**
```json
{
  "query": "查找转速信号",
  "project_key": "F2"  // 可选
}
```

**响应:**
```json
{
  "success": true,
  "query": "查找转速信号",
  "response": "AI生成的回答",
  "tool_calls": [
    {
      "function": "search_signals_fuzzy",
      "arguments": {"keyword": "转速"},
      "result": {...}
    }
  ]
}
```

### GET /api/projects
获取可用项目列表

### GET /api/health
系统健康检查

### GET /api/tools
获取可用MCP工具列表

## 项目结构

```
mcpclienttest/
├── main.py              # FastAPI主应用
├── mcp_client.py        # MCP客户端实现
├── run.py               # 启动脚本
├── requirements.txt     # Python依赖
├── .env                 # 环境变量配置
├── README.md           # 项目说明
└── static/
    └── index.html      # 前端页面
```

## 开发说明

### 添加新的MCP工具

1. 在 `mcp_client.py` 的 `MCPToolRegistry` 类中注册新工具
2. 在 `_register_default_tools` 方法中添加工具定义
3. 确保MCP服务器支持该工具

### 自定义前端样式

前端使用Tailwind CSS，可以通过修改 `static/index.html` 中的CSS类来自定义样式。

### 错误处理

应用包含完善的错误处理机制：
- MCP连接错误
- DeepSeek API调用错误
- 工具调用异常
- 网络超时处理

## 故障排除

1. **MCP服务器连接失败**
   - 检查 `MCP_SERVER_URL` 配置
   - 确认MCP服务器运行状态

2. **DeepSeek API调用失败**
   - 检查 `DEEPSEEK_API_KEY` 配置
   - 确认API密钥有效性

3. **前端页面无法加载**
   - 确认 `static/index.html` 文件存在
   - 检查静态文件服务配置

## 许可证

MIT License
