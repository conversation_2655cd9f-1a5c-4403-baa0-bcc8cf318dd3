#!/usr/bin/env python3
"""
MCP信号查询演示应用 - 演示脚本
"""

import webbrowser
import time
import subprocess
import sys
import os

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import httpx
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def start_server():
    """启动服务器"""
    print("🚀 启动MCP信号查询演示应用...")
    print("📍 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔧 健康检查: http://localhost:8000/api/health")
    print("\n按 Ctrl+C 停止服务\n")
    
    try:
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

def open_browser():
    """打开浏览器"""
    print("🌐 正在打开浏览器...")
    time.sleep(2)  # 等待服务器启动
    webbrowser.open("http://localhost:8000")

def show_demo_info():
    """显示演示信息"""
    print("=" * 60)
    print("🎯 MCP信号查询演示应用")
    print("=" * 60)
    print()
    print("📋 功能特性:")
    print("  • 智能对话界面 - 基于DeepSeek AI")
    print("  • MCP工具集成 - 远程CAN信号查询")
    print("  • 精美UI设计 - Tailwind CSS + Font Awesome")
    print("  • 实时交互展示 - 可视化AI与MCP交互")
    print()
    print("🔧 支持的查询类型:")
    print("  • 模糊搜索: '查找转速相关的信号'")
    print("  • 精确查询: '获取EngineSpeed信号详情'")
    print("  • 项目管理: '显示可用项目'")
    print()
    print("🌐 访问地址:")
    print("  • 主页面: http://localhost:8000")
    print("  • API文档: http://localhost:8000/docs")
    print("  • 健康检查: http://localhost:8000/api/health")
    print()
    print("📁 项目结构:")
    print("  • main.py - FastAPI主应用")
    print("  • mcp_client.py - MCP客户端实现")
    print("  • static/index.html - 前端页面")
    print("  • test_api.py - API测试脚本")
    print()

def main():
    """主函数"""
    show_demo_info()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  警告: DEEPSEEK_API_KEY 环境变量未设置")
    
    if not os.getenv("MCP_SERVER_URL"):
        print("⚠️  警告: MCP_SERVER_URL 环境变量未设置")
    
    print("\n🎬 准备启动演示...")
    input("按 Enter 键继续...")
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
