#!/usr/bin/env python3
"""
API测试脚本
"""

import requests
import json

def test_health():
    """测试健康检查"""
    print("测试健康检查...")
    response = requests.get("http://localhost:8000/api/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_projects():
    """测试获取项目列表"""
    print("测试获取项目列表...")
    response = requests.get("http://localhost:8000/api/projects")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_query(query_text):
    """测试查询接口"""
    print(f"测试查询: {query_text}")
    payload = {"query": query_text}
    response = requests.post(
        "http://localhost:8000/api/query",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"查询成功: {result.get('success', False)}")
        print(f"AI回复: {result.get('response', 'N/A')}")
        if result.get('tool_calls'):
            print(f"工具调用数量: {len(result['tool_calls'])}")
            for i, tool_call in enumerate(result['tool_calls']):
                print(f"  工具{i+1}: {tool_call['function']}")
    else:
        print(f"查询失败: {response.text}")
    print()

def main():
    """主测试函数"""
    print("=== MCP信号查询演示应用 API测试 ===\n")
    
    # 测试健康检查
    test_health()
    
    # 测试项目列表
    test_projects()
    
    # 测试查询
    test_queries = [
        "显示可用项目",
        "查找转速相关的信号",
        "获取项目统计信息"
    ]
    
    for query in test_queries:
        test_query(query)

if __name__ == "__main__":
    main()
